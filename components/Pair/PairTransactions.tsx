import BigNumber from "bignumber.js";
import { get, isEmpty, uniqBy, uniqWith } from "lodash";
import * as React from "react";
import { useContext, useEffect, useRef, useState } from "react";
import { useSelector } from "react-redux";
import { useMediaQuery } from "react-responsive";
import { CloseAuditCheckIcon, ExternalLink, FilterIcon } from "@/assets/icons";
import {
  AppAvatarTokenQuote,
  AppNumber,
  AppSwapUnit,
  AppSymbolToken,
  AppTimeDisplay,
  AppUserAddress,
} from "@/components";
import { AppDataTableRealtime } from "@/components/AppDataTableRealtime";
import AppDateTypeSwitch from "@/components/AppDateTypeSwitch";
import {
  AppBroadcast,
  BROADCAST_EVENTS,
  TBroadcastEvent,
} from "@/libs/broadcast";
import Storage from "@/libs/storage";
import rf from "@/services/RequestFactory";
import { RootState } from "@/store";
import { TPair, TPairPrice, TPairTransaction } from "@/types";
import { DATE_TYPE } from "@/utils/contants";
import { formatToPercent } from "@/utils/format";
import {
  dividedBN,
  filterParams,
  getLinkTxExplorer,
  multipliedBN,
} from "@/utils/helper";
import {
  FilterByAmount,
  FilterByMaker,
  FilterByTotal,
  FilterByType,
} from "@/components/FilterTransactions";
import TooltipMakerInsight, {
  DetailMarker,
} from "@/components/Pair/TooltipMakerInsight";
import { useSearchParams } from "next/navigation";
import { usePairPrice } from "@/hooks/usePairPrice";

const UNIT_TYPE = {
  USD: "USD",
  TOKEN: "TOKEN",
};

const SHOW_TYPE = {
  TABLE: "table",
  COMPACT: "compact",
};

const TypeTx = ({ type }: { type: string }) => {
  const isMobile = useMediaQuery({ query: "(max-width: 992px)" });

  const getDisplayType = (type: string): string => {
    if (isMobile) {
      return type.charAt(0).toUpperCase();
    }
    return type.toLowerCase();
  };

  const displayType = getDisplayType(type);

  const isBuyOrAdd = type === "BUY" || type === "ADD";

  return (
    <div
      className={`rounded-[4px] px-1 py-[1px] capitalize ${
        isBuyOrAdd ? "bg-green-800 text-green-500" : "bg-red-800 text-red-500"
      }`}
    >
      {displayType}
    </div>
  );
};

const TotalTxUsdBySui = ({ activity }: { activity: TPairTransaction }) => {
  const suiPriceUsd = useSelector(
    (state: RootState) => state.metadata.suiPriceUsd
  );

  return (
    <AppNumber
      value={multipliedBN(activity?.quoteAmount, suiPriceUsd)}
      isForUSD
    />
  );
};

const TotalTx = ({
  activity,
  totalUnit,
  pair,
  className,
}: {
  activity: TPairTransaction;
  totalUnit: string;
  className?: string;
  pair: TPair;
}) => {
  const percentPrice = (amount: string | number) => {
    if (!pair || !amount) return "0%";
    return formatToPercent(dividedBN(amount, Number(pair.liquidityUsd) / 20));
  };

  const getBg = () => {
    if (activity.tradingType === "SELL" || activity.tradingType === "REMOVE") {
      return "rgba(244, 91, 91, 0.06)";
    }
    return "rgba(72, 187, 120, 0.10)";
  };

  const _renderContent = () => {
    if (totalUnit === UNIT_TYPE?.USD) {
      if (activity.tradingType === "REMOVE" || activity.tradingType === "ADD") {
        return <TotalTxUsdBySui activity={activity} />;
      }
      return <AppNumber value={activity.totalUsd} isForUSD />;
    }

    return (
      <>
        <AppAvatarTokenQuote pair={pair} />
        <AppNumber
          value={activity?.quoteAmount}
          decimals={pair?.tokenQuote?.decimals}
        />
      </>
    );
  };

  return (
    <div
      className={`${className}`}
      style={{
        background: `linear-gradient(to right, ${getBg()} ${percentPrice(
          activity.totalUsd
        )}, rgba(0, 0, 0, 0) ${percentPrice(activity.totalUsd)})`,
      }}
    >
      {_renderContent()}
    </div>
  );
};

const TabFilterMaker = ({
  value,
  onChange,
}: {
  value: string;
  onChange: (value: string) => void;
}) => {
  const OPTIONS_TYPE = [
    {
      value: "All",
      label: "All",
    },
    {
      value: "Dev",
      label: "Dev",
    },
    {
      value: "Tracker",
      label: "Tracker",
    },
  ];

  return (
    <div className={`bg-neutral-alpha-50 flex rounded-[4px] p-[2px]`}>
      {OPTIONS_TYPE.map((item, index) => (
        <div
          key={index}
          className={`cursor-pointer rounded-[4px] p-1 text-center text-[12px] leading-[1.5] md:min-w-[35px]  ${
            value === item.value
              ? `bg-neutral-alpha-50 text-neutral-alpha-1000`
              : `text-neutral-alpha-400`
          }`}
          onClick={() => onChange(item.value)}
        >
          {item.label}
        </div>
      ))}
    </div>
  );
};

export default AppDateTypeSwitch;

export const PairTransactions = ({
  heightContent,
  type,
  pair,
}: {
  heightContent: number | string;
  type: string;
  pair: TPair;
}) => {
  const { pairPrice } = usePairPrice(pair);
  const [dateType, setDateType] = useState<string>("Age");
  const userSettings = Storage.getUserSettings();
  const searchParams = useSearchParams();
  const dataTableRef = useRef<HTMLDivElement>(null);
  const [priceUnit, setPriceUnit] = useState<string>(
    userSettings.priceUnit || UNIT_TYPE.USD
  );
  const [totalUnit, setTotalUnit] = useState<string>(
    userSettings.totalUnit || UNIT_TYPE.USD
  );
  const walletTracker = useSelector(
    (state: RootState) => state.user.walletTracker
  );

  const [makerType, setMakerType] = useState<string>("All");

  const [showTxnDetail, setShowTxnDetail] = useState<string>("");

  const isDesktop = useMediaQuery({ query: "(min-width: 992px)" });

  const [params, setParams] = useState<any>({
    tradingType: "ALL",
    makerAddress: searchParams.get("makerAddress") || "",
    totalUsd: "",
    baseAmount: "",
    quoteAmount: "",
  });
  const paramsRef = useRef(params);
  const pairRef = useRef(pair);

  useEffect(() => {
    Storage.setUserSettings("priceUnit", priceUnit || UNIT_TYPE.USD);
    Storage.setUserSettings("totalUnit", totalUnit || UNIT_TYPE.USD);
  }, [priceUnit, totalUnit]);

  useEffect(() => {
    pairRef.current = pair;
    paramsRef.current = params;
    if (isEmpty(pair)) return;

    (dataTableRef?.current as any)?.refresh();
  }, [pair?.pairId, params]);

  const isFiltering = () => {
    if (!paramsRef?.current) {
      return false;
    }
    return Object.values(paramsRef?.current).some(
      (value) => value !== "" && value !== "ALL"
    );
  };

  const getTransactions = async (dataTableParams: any) => {
    const pairSlug = pairRef?.current?.slug || pair.slug;
    if (!pairSlug) {
      return { data: [] };
    }
    const paramsFilter = filterParams({
      pairIdOrSlug: pairRef.current?.pairId,
      toTimestamp: dataTableParams?.toTimestamp,
      ...paramsRef.current,
      ...dataTableParams,
    });

    const res = await rf
      .getRequest("TransactionRequest")
      .getTransactions(pair.network, paramsFilter);
    const filteredDupRecord = uniqBy(
      res?.docs || [],
      (item: any) => `${item.hash}-${item.index}`
    );
    const makerAddresses = uniqWith(
      filteredDupRecord.map((item) => item.maker.address)
    );
    const domains = await rf
      .getRequest("DomainRequest")
      .getDomainNamesByAddresses(makerAddresses);

    if (!!domains.length) {
      const allDomains: any = {};
      for (let i = 0; i < makerAddresses.length; i++) {
        allDomains[makerAddresses[i]] = domains[i];
      }
      return {
        data: filteredDupRecord.map((item: any) => {
          const alias = get(allDomains, item.maker.address, item.maker.address);
          return {
            ...item,
            alias: alias.startsWith("0x") ? undefined : alias,
          };
        }),
      };
    }
    return { data: filteredDupRecord };
  };

  const canAppendActivity = (data: TPairTransaction): boolean => {
    if (!isFiltering() || !paramsRef?.current) return true;

    const { tradingType, makerAddress, totalUsd, baseAmount, quoteAmount } =
      paramsRef.current;

    if (tradingType !== "ALL" && data.tradingType !== tradingType) return false;

    if (
      makerAddress &&
      !data.maker?.address?.toLowerCase().includes(makerAddress.toLowerCase())
    )
      return false;

    const checkRange = (value: string, range: string) => {
      if (!range) return true;
      const [min, max] = range.split("-").map(Number);
      const bnValue = new BigNumber(value);
      return (
        bnValue.isGreaterThanOrEqualTo(min) && bnValue.isLessThanOrEqualTo(max)
      );
    };

    if (totalUsd && !checkRange(data.totalUsd, totalUsd)) return false;
    if (baseAmount && !checkRange(data.baseAmount, baseAmount)) return false;
    if (quoteAmount && !checkRange(data.quoteAmount, quoteAmount)) return false;

    return true;
  };

  const isNewTransaction = (activity: TPairTransaction) => {
    if (!dataTableRef.current) return false;
    const newData = (dataTableRef?.current as any)?.getNewData();
    return newData.some(
      (newTransaction: TPairTransaction) =>
        newTransaction.hash === activity.hash
    );
  };

  const handleWhenRefreshData = (event: TBroadcastEvent) => {
    (dataTableRef.current as any)?.refresh();
  };

  useEffect(() => {
    AppBroadcast.on(BROADCAST_EVENTS.REFRESH_DATA, handleWhenRefreshData);
    AppBroadcast.on(
      BROADCAST_EVENTS.CLOSE_FILTER_MARKER,
      handleCloseFilterMaker
    );
    return () => {
      AppBroadcast.remove(BROADCAST_EVENTS.REFRESH_DATA, handleWhenRefreshData);
      AppBroadcast.remove(
        BROADCAST_EVENTS.CLOSE_FILTER_MARKER,
        handleWhenRefreshData
      );
    };
  }, []);

  const handleCloseFilterMaker = () => {
    if (typeof window === "undefined") {
      return;
    }
    const searchParams = new URLSearchParams(window.location.search);
    searchParams.delete("makerAddress");
    window.history.replaceState(
      {},
      "",
      `${window.location.pathname}?${searchParams}`
    );
    setParams({
      ...params,
      makerAddress: "",
    });
    AppBroadcast.dispatch(BROADCAST_EVENTS.FILTER_MAKER_IN_CHART, {
      makerAddress: "",
    });
  };

  const isTypeCompact = type === SHOW_TYPE.COMPACT;

  const onShowDetailTxn = (activity: TPairTransaction) => {
    if (!isTypeCompact) return;
    if (showTxnDetail === activity.hash) {
      setShowTxnDetail("");
      return;
    }
    setShowTxnDetail(activity.hash);
  };

  useEffect(() => {
    if (makerType === "All") {
      setParams({ ...params, makerAddress: "" });
    }
    if (makerType === "Dev") {
      setParams({ ...params, makerAddress: pair?.tokenBase?.deployer });
    }
    if (makerType === "Tracker") {
      const walletsName = walletTracker?.wallets?.map((w) => w?.walletAddress);
      setParams({ ...params, makerAddress: walletsName?.join(",") });
    }
  }, [makerType, walletTracker?.wallets]);

  const Row = React.memo(
    ({ style, activity }: { style: any; activity: TPairTransaction }) => (
      <div
        style={style}
        className={`border-neutral-alpha-50 w-full border-b text-[12px] font-normal leading-[1.5] ${
          activity.tradingType === "SELL" || activity.tradingType === "REMOVE"
            ? "text-red-500"
            : "text-green-500"
        } ${isNewTransaction(activity) ? "animate-new-transaction" : ""}`}
      >
        <div
          className={`${
            isTypeCompact ? "" : "hover:bg-neutral-alpha-50"
          } flex w-full `}
          onClick={() => onShowDetailTxn(activity)}
        >
          {isTypeCompact && (
            <div className="td w-[5%] px-1">
              <TypeTx type={activity.tradingType} />
            </div>
          )}

          <div
            className={`w-[${
              isTypeCompact ? "19" : "14"
            }%] td text-neutral-alpha-500`}
          >
            <AppTimeDisplay
              timestamp={activity.timestamp * 1000}
              isAgo={dateType === DATE_TYPE.AGE}
            />
          </div>

          {!isTypeCompact && (
            <div className="td w-[10%]">
              <TypeTx type={activity.tradingType} />
            </div>
          )}

          <TotalTx
            activity={activity}
            pair={pair}
            totalUnit={totalUnit}
            className={`td w-[${isTypeCompact ? "26" : "18"}%]`}
          />

          {!isTypeCompact && (
            <div className="td w-[18%]">
              <AppNumber
                value={activity.baseAmount}
                decimals={pair?.tokenBase?.decimals}
              />
            </div>
          )}

          <div className={`td w-[${isTypeCompact ? "22" : "18"}%]`}>
            {priceUnit === UNIT_TYPE.USD ? (
              <AppNumber value={activity.priceUsd} isForUSD />
            ) : (
              <>
                <AppAvatarTokenQuote pair={pair} />
                <AppNumber
                  value={activity?.price}
                  decimals={pair?.tokenBase?.decimals}
                />
              </>
            )}
          </div>

          <div
            className={`w-[${
              isTypeCompact ? "28" : "22"
            }%] td text-neutral-alpha-800`}
          >
            <div className="h-4 w-4">
              <TooltipMakerInsight
                type={type}
                pair={pair}
                pairPrice={pairPrice}
                makerAddress={activity?.maker?.address}
                category={activity?.maker?.traderCategory}
              />
            </div>

            <AppUserAddress
              className={isTypeCompact ? "body-2xs-regular-8" : ""}
              network={pair?.network}
              address={activity.maker?.address}
              alias={(activity as any)?.alias?.replace(".sui", "")}
            />

            <div className="text-neutral-alpha-800 hover:text-neutral-0 cursor-pointer ">
              {params.makerAddress ? (
                <CloseAuditCheckIcon onClick={handleCloseFilterMaker} />
              ) : (
                <FilterIcon
                  onClick={() => {
                    setParams({
                      ...params,
                      makerAddress: activity.maker?.address,
                    });
                    const searchParams = new URLSearchParams(
                      window.location.search
                    );
                    searchParams.set("makerAddress", activity.maker?.address);
                    window.history.replaceState(
                      {},
                      "",
                      `${window.location.pathname}?${searchParams}`
                    );
                    AppBroadcast.dispatch(
                      BROADCAST_EVENTS.FILTER_MAKER_IN_CHART,
                      {
                        makerAddress: activity.maker?.address,
                      }
                    );
                  }}
                />
              )}
            </div>

            <a
              href={getLinkTxExplorer(pair.network, activity.hash)}
              target="_blank"
              className="text-neutral-alpha-800 hover:text-neutral-0"
            >
              <ExternalLink />
            </a>
          </div>
        </div>

        {showTxnDetail === activity.hash && isTypeCompact && (
          <div className="bg-white-50 border-white-50 border-t">
            <DetailMarker
              activity={activity}
              type={type}
              pair={pair}
              pairPrice={pairPrice}
              markerAddress={activity?.maker?.address}
              category={activity?.maker?.traderCategory}
            />
          </div>
        )}
      </div>
    ),
    (prevProps, nextProps) => {
      return prevProps.activity.hash === nextProps.activity.hash;
    }
  );
  Row.displayName = "Row";

  const getMinWidth = () => {
    if (!isTypeCompact) {
      if (isDesktop) {
        return 790;
      }
      return 600;
    }
    return 375;
  };

  return (
    <div>
      {pair?.slug && (
        <AppDataTableRealtime
          minWidth={getMinWidth()}
          shouldAutoFetchOnInit={false}
          ref={dataTableRef}
          height={heightContent}
          getData={getTransactions}
          overrideBodyClassName={"w-full"}
          handleAddNewItem={{
            broadcastName: BROADCAST_EVENTS.TRANSACTION_CREATED,
            fieldKey: "hash",
            formatter: (data: any) => {
              if (data.pairId !== pair.pairId) {
                return;
              }
              if (!canAppendActivity(data)) {
                return;
              }
              return data;
            },
          }}
          renderHeader={() => {
            if (!isTypeCompact) {
              return (
                <>
                  <div className="thead w-[14%]">
                    <AppDateTypeSwitch
                      selectedValue={dateType}
                      onSelect={setDateType}
                      activeClassName="text-white custom-opacity-svg-active"
                      inactiveClassName="custom-opacity-svg-inactive"
                    />
                  </div>

                  <div className="thead w-[10%]">
                    Type <FilterByType params={params} setParams={setParams} />
                  </div>

                  <div className="thead w-[18%]">
                    Total{" "}
                    <AppSwapUnit
                      unit={totalUnit}
                      setUnit={setTotalUnit}
                      pair={pair}
                    />
                    <FilterByTotal params={params} setParams={setParams} />
                  </div>

                  <div className="thead w-[18%]">
                    <AppSymbolToken
                      symbol={pair?.tokenBase?.symbol?.toUpperCase()}
                    />
                    <FilterByAmount
                      pair={pair}
                      params={params}
                      setParams={setParams}
                      isBaseAmount
                    />
                  </div>

                  <div className="thead w-[18%]">
                    Price{" "}
                    <AppSwapUnit
                      unit={priceUnit}
                      setUnit={setPriceUnit}
                      pair={pair}
                    />
                  </div>
                  <div className="thead w-[22%] flex-1">
                    <TabFilterMaker value={makerType} onChange={setMakerType} />
                    <FilterByMaker params={params} setParams={setParams} />
                  </div>
                </>
              );
            }
            return (
              <>
                <div className="thead w-[5%]" />
                <div className="thead w-[19%]">
                  <AppDateTypeSwitch
                    selectedValue={dateType}
                    onSelect={setDateType}
                    activeClassName="text-white custom-opacity-svg-active"
                    inactiveClassName="custom-opacity-svg-inactive"
                  />
                </div>

                <div className="thead body-xs-regular-10 w-[26%]">
                  Total{" "}
                  <AppSwapUnit
                    unit={totalUnit}
                    setUnit={setTotalUnit}
                    pair={pair}
                  />
                  <FilterByTotal params={params} setParams={setParams} />
                </div>

                <div className="thead body-xs-regular-10 w-[22%]">
                  Price{" "}
                  <AppSwapUnit
                    unit={priceUnit}
                    setUnit={setPriceUnit}
                    pair={pair}
                  />
                </div>
                <div className="thead body-xs-regular-10 w-[28%] flex-1">
                  Makers <FilterByMaker params={params} setParams={setParams} />
                </div>
              </>
            );
          }}
          renderRow={(item: TPairTransaction, index: number) => {
            return <Row key={item.hash} style={{}} activity={item} />;
          }}
        />
      )}
    </div>
  );
};
