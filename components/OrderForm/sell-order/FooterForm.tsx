import { ButtonSignIn } from "@/components/OrderForm/ButtonSignin";
import { ButtonAddWallet } from "@/components/OrderForm/ButtonAddWallet";
import { ButtonAddFund } from "@/components/OrderForm/ButtonAddFund";
import { getDexToWhenAfterGraduated, isDexHasBondingCurve } from "@/utils/dex";
import BigNumber from "bignumber.js";
import { formatNumber } from "@/utils/format";
import { AppButton } from "@/components";
import { AggregatorToggle } from "../AggregatorToggle";
import { dividedBN } from "@/utils/helper";
import * as React from "react";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import { useOrder, useUser } from "@/hooks";
import { TPair } from "@/types";
import { CoinTip, GasIcon, SettingsIcon, SlippageIcon } from "@/assets/icons";
import { OrderFormType } from "@/enums";
import { TRADE_TYPE } from "@/enums/trade.enum";

export const FooterForm = ({
  sellPercent,
  sellType,
  createOrder,
  totalBalanceTokenBase,
  onShowSettings,
  tokenAmountEstimate,
  amountOrders,
  amountTime,
  resolution,
  pair,
  useAggregator,
  setUseAggregator,
}: {
  sellType: string;
  amountOrders: any;
  amountTime: any;
  resolution: string;
  sellPercent: any;
  totalBalanceTokenBase: any;
  createOrder: () => void;
  onShowSettings: () => void;
  tokenAmountEstimate: any;
  pair: TPair;
  useAggregator: boolean;
  setUseAggregator: (value: boolean) => void;
}) => {
  const accessToken = useSelector((state: RootState) => state.user.accessToken);
  const wallets = useSelector((state: RootState) => state.user.wallets);
  const { activeTotalSuiBalance } = useUser();

  const { settings } = useOrder();

  const _renderButton = () => {
    if (!accessToken) {
      return (
        <ButtonSignIn type={TRADE_TYPE.SELL} className="tablet:mt-2 !mt-0" />
      );
    }

    if (!wallets.length) {
      return (
        <ButtonAddWallet type={TRADE_TYPE.SELL} className="tablet:mt-2 !mt-0" />
      );
    }

    if (!+activeTotalSuiBalance) {
      return (
        <ButtonAddFund text={"Add Gas Fee"} className="tablet:mt-2 !mt-0" />
      );
    }

    if (+sellPercent > 100) {
      return <ButtonAddFund className="tablet:mt-2 !mt-0" />;
    }
    if (
      isDexHasBondingCurve(pair?.dex?.dex) &&
      Number(pair?.bondingCurve || 0) >= 1
    ) {
      return (
        <AppButton size="large" disabled variant="buy">
          {pair?.graduatedSlug
            ? `Migrated to ${getDexToWhenAfterGraduated(pair?.dex?.dex)}`
            : `Migrating to ${getDexToWhenAfterGraduated(pair?.dex?.dex)}`}
        </AppButton>
      );
    }

    const sellAmount = new BigNumber(dividedBN(sellPercent, 100)).multipliedBy(
      totalBalanceTokenBase
    );

    if (sellType === OrderFormType.DCA) {
      return (
        <div
          className="tablet:mt-2 action-sm-medium-14 border-white-150 bg-brand-800 mt-0 flex h-[40px] cursor-pointer flex-col items-center justify-center rounded-[6px] border px-2"
          onClick={createOrder}
        >
          <div className="md:body-md-medium-14 body-sm-medium-12 text-brand-500">
            Sell{" "}
            {!!+sellPercent && `${sellPercent}% ${pair?.tokenBase?.symbol}`}
          </div>
          {amountTime && amountOrders && (
            <div className="body-xs-medium-10 text-white-500">
              every {amountTime}
              {resolution.toLowerCase()} over {amountOrders} orders
            </div>
          )}
        </div>
      );
    }

    return (
      <div
        className="tablet:mt-2 action-sm-medium-14 border-white-150 bg-brand-800 mt-0 flex h-[40px] cursor-pointer flex-col items-center justify-center rounded-[6px] border px-2"
        onClick={createOrder}
      >
        <div className="md:body-md-medium-14 body-sm-medium-12 text-brand-500">
          Sell {!!+sellAmount && `${sellAmount} ${pair?.tokenBase?.symbol}`}
        </div>
        {!!+tokenAmountEstimate && (
          <div className="body-xs-medium-10 text-white-500">
            (≈
            {formatNumber(tokenAmountEstimate, pair?.tokenQuote.decimals)}{" "}
            {pair?.tokenQuote?.symbol})
          </div>
        )}
      </div>
    );
  };

  const getTipAmount = () => {
    if (sellType === OrderFormType.LIMIT) {
      return settings.limitSellTipAmount || "--";
    }

    return settings.quickSellTipAmount || "--";
  };

  const getSlippage = () => {
    if (sellType === OrderFormType.LIMIT) {
      return settings.limitSellSlippage || "--";
    }

    return settings.quickSellSlippage || "--";
  };

  const getGasPrice = () => {
    if (sellType === OrderFormType.LIMIT) {
      return settings.limitSellGasPrice || "--";
    }

    return settings.quickSellGasPrice || "--";
  };

  return (
    <>
      <div className="mb-3 mt-9 flex items-center justify-between gap-2">
        <div className="flex gap-2">
          <div className="text-neutral-alpha-500 body-sm-medium-12 flex w-max items-center gap-[4px]">
            <GasIcon />
            <div>{getGasPrice()}</div>
          </div>
          <div className="text-neutral-alpha-500 body-sm-medium-12 border-white-50 flex w-max items-center gap-[4px] border-l pl-2">
            <CoinTip />
            <div>{getTipAmount()}</div>
          </div>

          <div className="text-neutral-alpha-500 body-sm-medium-12 flex gap-1">
            <SlippageIcon />
            {getSlippage()}%
          </div>
        </div>
        <div className="tablet:hidden w-full">{_renderButton()}</div>

        <div
          onClick={onShowSettings}
          className="border-neutral-alpha-500 tablet:p-0 tablet:border-none text-neutral-alpha-500 body-sm-medium-12 hover:text-neutral-alpha-1000 flex cursor-pointer gap-1 rounded-md border border-solid p-2"
        >
          <SettingsIcon className="h-5 w-5" />
          <span className="tablet:inline hidden">Settings</span>
        </div>
      </div>

      <div className="tablet:block hidden">{_renderButton()}</div>
    </>
  );
};
