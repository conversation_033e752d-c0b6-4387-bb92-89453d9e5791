import React, { useEffect, useState } from "react";
import { TP<PERSON>, TTrader } from "@/types";
import rf from "@/services/RequestFactory";
import { minusBN, multipliedBN } from "@/utils/helper";
import BigNumber from "bignumber.js";
import { formatNumber, formatShortAddress } from "@/utils/format";
import { AppNumber, AppProgressSingle, AppCopy } from "@/components";
import { CATEGORY_MARKER } from "@/utils/contants";
import Tooltip from "rc-tooltip";
import {
  DolphinIcon,
  PlanktonIcon,
  ShirmpIcon,
  WhaleIcon,
  FishIcon,
} from "@/assets/icons";
import { TTransaction } from "@/types/wallet-tracker";

export const DetailMarker = ({
  pair,
  markerAddress,
  priceUsd,
}: {
  pair: TPair;
  markerAddress: string;
  priceUsd: string;
}) => {
  const [data, setData] = useState<TTrader>();

  const getInfoTraders = async () => {
    try {
      const res = await rf
        .getRequest("TradersRequest")
        .getInfoTraders(pair?.network, pair?.pairId, {
          pair: pair?.pairId,
          addresses: [markerAddress],
        });
      setData(res.docs[0]);
    } catch (e) {
      console.error(e);
    }
  };

  useEffect(() => {
    if (!pair) return;
    getInfoTraders().then();
  }, [pair?.pairId, markerAddress]);

  const pnl = minusBN(data?.volumeUsdSold || 0, data?.volumeUsdBought || 0);

  const totalUnrealized = minusBN(
    data?.baseAmountBought || 0,
    data?.baseAmountSold || 0
  );

  const totalUnrealizedByUSD = () => {
    if (new BigNumber(totalUnrealized).comparedTo(0) < 0) {
      return "0";
    }

    return multipliedBN(totalUnrealized, priceUsd);
  };

  const _renderBalance = () => {
    if (!data?.baseAmountBought || +totalUnrealized < 0)
      return (
        <div className="body-xs-regular-10 text-neutral-alpha-500">Unknown</div>
      );
    return (
      <div className="flex min-w-[93px] flex-col items-center">
        <div className="body-xs-regular-10">
          {!!+totalUnrealized ? formatNumber(totalUnrealized, 2) : "0"}{" "}
          <span className="text-neutral-alpha-500 body-xs-regular-10 px-[2px]">
            of
          </span>
          {formatNumber(data.baseAmountBought || 0, 2)}
        </div>
        <AppProgressSingle
          height={2}
          value={totalUnrealized}
          total={data.baseAmountBought || 0}
        />
      </div>
    );
  };

  // const getRule = () => {
  //   switch (category) {
  //     case CATEGORY_MARKER.PLANKTON: {
  //       return "Plankton:  <$10 bought or sold";
  //     }
  //     case CATEGORY_MARKER.SHRIMP: {
  //       return "Shrimp: $250 - $1k bought or sold";
  //     }
  //     case CATEGORY_MARKER.FISH: {
  //       return "Fish: $10 - $250 bought or sold";
  //     }
  //     case CATEGORY_MARKER.DOLPHIN: {
  //       return "Dolphin: $1k - $10k bought or sold";
  //     }
  //     case CATEGORY_MARKER.WHALE: {
  //       return "Whale: >$10k bought or sold";
  //     }
  //     default: {
  //       return "";
  //     }
  //   }
  // };

  return (
    <div className="w-full">
      <table className="w-full">
        <tbody>
          <tr className="body-xs-regular-10 border-neutral-alpha-50 border-b border-dashed">
            <td className="w-[72px] py-2">
              <span className="text-neutral-alpha-500 pr-1">(+)</span> Invested
            </td>
            <td className="body-sm-semibold-12 py-2 text-red-500">
              <AppNumber value={data?.volumeUsdBought} isForUSD />
            </td>
            <td className="px-4 py-2">
              <AppNumber value={data?.baseAmountBought} />
            </td>
            <td className="text-neutral-alpha-500 py-2 pl-4">
              {data?.boughtTxs} txn
            </td>
          </tr>

          <tr className="body-xs-regular-10 border-neutral-alpha-50 border-b border-dashed px-4">
            <td className="w-[72px] py-2">
              <span className="text-neutral-alpha-500 pr-1">(-)</span> Sold
            </td>
            <td className="body-sm-semibold-12 py-2 text-green-500">
              <AppNumber value={data?.volumeUsdSold} isForUSD />
            </td>
            <td className="px-4 py-2">
              <AppNumber value={data?.baseAmountSold} />
            </td>
            <td className="text-neutral-alpha-500 py-2 pl-4">
              {data?.soldTxs} txn
            </td>
          </tr>

          <tr className="body-xs-regular-10 border-neutral-alpha-50 border-b border-dashed">
            <td className="w-[72px] py-2">
              <span className="text-neutral-alpha-500 pr-1">(=)</span> P&L
            </td>
            <td
              className={`body-sm-semibold-12 py-2 ${
                +pnl > 0 ? "text-green-500" : "text-red-500"
              }`}
            >
              <AppNumber value={new BigNumber(pnl).abs().toString()} isForUSD />
            </td>
          </tr>

          <tr className="body-xs-regular-10 border-neutral-alpha-50 border-b border-dashed">
            <td className="w-[72px] py-2">
              <div>Unrealized</div>
            </td>
            <td className="body-sm-semibold-12 py-2">
              <AppNumber value={totalUnrealizedByUSD()} isForUSD />
            </td>
            <td colSpan={2} className="pl-4">
              {_renderBalance()}
            </td>
          </tr>
        </tbody>
      </table>

      <div className="mt-2 flex items-center gap-2">
        {/*<CategoryMarker category={category} width={32} />*/}
        <div>
          <div className="text-neutral-alpha-1000 body-sm-semibold-12">
            {formatShortAddress(markerAddress, 6, 6)}
          </div>

          {/*<div className="body-xs-regular-10 text-neutral-alpha-500">*/}
          {/*  {getRule()}*/}
          {/*</div>*/}
          {/*<div className="body-xs-regular-10 text-neutral-alpha-500">*/}
          {/*  Holder Since:: 5h 50m*/}
          {/*</div>*/}
        </div>
        <AppCopy
          message={markerAddress}
          className="text-white-500 cursor-pointer"
        />
      </div>
    </div>
  );
};

const CategoryMarker = ({
  category,
  width,
}: {
  category: string;
  width: number;
}) => {
  if (category === CATEGORY_MARKER.SHRIMP) {
    return <ShirmpIcon className={`w-[${width}px] h-[${width}px]`} />;
  }

  if (category === CATEGORY_MARKER.WHALE) {
    return <WhaleIcon className={`w-[${width}px] h-[${width}px]`} />;
  }

  if (category === CATEGORY_MARKER.DOLPHIN) {
    return <DolphinIcon className={`w-[${width}px] h-[${width}px]`} />;
  }

  if (category === CATEGORY_MARKER.PLANKTON) {
    return <PlanktonIcon className={`w-[${width}px] h-[${width}px]`} />;
  }

  return <FishIcon className={`w-[${width}px] h-[${width}px]`} />;
};

interface TooltipWalletNameProps {
  transaction: TTransaction;
  wallet: any;
}

export const TooltipWalletName: React.FC<TooltipWalletNameProps> = ({
  transaction,
  wallet,
}) => {
  return (
    <div className="px-2 py-[10px]">
      <Tooltip
        overlay={
          <DetailMarker
            priceUsd={transaction?.priceUsd}
            pair={transaction?.pair}
            markerAddress={transaction?.walletAddress}
          />
        }
        placement={"right"}
        showArrow={false}
      >
        <div className="max-w-[150px] cursor-pointer truncate">
          {wallet?.walletName || "--"}
        </div>
      </Tooltip>
    </div>
  );
};

export default TooltipWalletName;
