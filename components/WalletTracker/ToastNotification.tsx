import { AppAvatarToken } from "../AppAvatarToken";
import React from "react";
import { DEXS, getDexLogoUrl } from "@/utils/dex";
import { convertMistToDec } from "@/utils/helper";
import { NETWORKS, SUI_DECIMALS } from "@/utils/contants";
import { AppLogoNetwork } from "../AppLogoNetwork";
import { AppNumber } from "../AppNumber";
import { AppTimeDisplay } from "../AppTimeDisplay";
import Link from "next/link";

const ToastNotification = ({
  transaction,
  walletName,
}: {
  transaction: any;
  walletName?: string;
}) => {
  const dexKey = (transaction?.pair?.dex?.dex ||
    transaction?.pair?.dex ||
    "") as keyof typeof DEXS;
  const dexLogoUrl = getDexLogoUrl(dexKey);

  return (
    <Link href={`/${transaction?.pair?.network}/${transaction?.pair?.slug}`}>
      <div className="rounded-8 bg-brand-1000 relative flex !w-full gap-3 px-4 py-3">
        <div className=" flex items-center gap-2">
          <div className="relative">
            <AppAvatarToken
              image={transaction.imageToken || transaction.iconUrl}
              size={36}
            />
            <div
              className="body-xs-medium-10 text-white-1000 absolute bottom-[-4px] left-[-4px] rounded-[8px] px-[4px]"
              style={{
                background: "rgba(8, 9, 12, 0.80)",
              }}
            >
              <AppTimeDisplay
                timestamp={transaction.timestamp}
                isAgo
                suffix=""
              />
            </div>

            {transaction?.pair?.dex && (
              <div
                className="absolute bottom-[-4px] right-[-4px] rounded-full p-[2px] "
                style={{
                  background: "rgba(8, 9, 12, 0.80)",
                }}
              >
                <img
                  src={dexLogoUrl}
                  className="h-[12px] w-[12px]"
                  alt={`${transaction?.pair?.dex?.dex} logo`}
                />
              </div>
            )}
          </div>

          <div className="body-md-medium-14 text-white-1000 flex max-w-[236px] flex-wrap items-center gap-1">
            {walletName || "--"}{" "}
            <span
              className={
                transaction?.tradingType === "SELL"
                  ? "text-red-500"
                  : "text-green-500"
              }
            >
              {transaction?.tradingType === "SELL"
                ? "sold some"
                : "bought more"}
            </span>{" "}
            {transaction.tokenSymbol}
            <AppLogoNetwork network={NETWORKS.SUI} isBase />
            <span
              className={
                transaction?.tradingType === "SELL"
                  ? "text-red-500"
                  : "text-green-500"
              }
            >
              <AppNumber
                value={convertMistToDec(transaction.amount, SUI_DECIMALS)}
              />
            </span>{" "}
            at{" "}
            <AppNumber
              value={transaction?.pair?.marketCapUsd}
              isForUSD
              decimals={4}
            />
          </div>
        </div>

        <div
          className={`rounded-8 absolute left-[-4px] top-0 z-[-1] h-full w-4 bg-green-500`}
        />
      </div>
    </Link>
  );
};
export default ToastNotification;
