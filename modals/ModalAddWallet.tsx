"use client";

import React, { useState } from "react";
import AppInput from "@/components/AppInput";
import { AppButton } from "@/components";
import rf from "@/services/RequestFactory";
import { toastError, toastSuccess } from "@/libs/toast";
import { isValidSuiAddress } from "@/utils/helper";
import { TGroup } from "../types/wallet-tracker";
import AppModal from "./AppModal";

const ModalAddWallet = ({
  onClose,
  group,
  fetchGroups,
  isOpen,
  onFetchData,
}: {
  isOpen: boolean;
  group?: TGroup;
  onClose: () => void;
  fetchGroups: () => void;
  onFetchData: () => void;
}) => {
  const [walletName, setWalletName] = useState<string>("");
  const [walletAddress, setWalletAddress] = useState<string>("");

  const addWallets = async () => {
    try {
      let dataGroup = group;
      if (!dataGroup) {
        dataGroup = await rf.getRequest("WalletTrackerRequest").addGroup({
          color: "#00FFCC",
          description: "Group Main",
          name: "main",
        });
      }
      if (dataGroup) {
        await rf.getRequest("WalletTrackerRequest").addWallet({
          groupId: dataGroup?.id,
          walletAddress,
          walletName,
        });
        toastSuccess("Success", "Add Successfully!");
        onClose();
        fetchGroups();
        onFetchData();
      }
    } catch (e: any) {
      toastError("Error", e?.message || "Something went wrong!");
      console.log(e);
    }
  };

  return (
    <AppModal
      isOpen={isOpen}
      title="Add Wallet"
      onClose={onClose}
      className="w-[343px] max-w-[375px]"
    >
      <div className="mt-[22px] gap-4">
        <div className="mb-4">
          <AppInput
            value={walletAddress}
            onChange={(e: any) => setWalletAddress(e.target.value?.trim())}
            label="Wallet Address"
            placeholder="Enter here"
            rootClassName="!h-[34px] !bg-transparent"
            labelClassName="text-white-700 action-sm-medium-12"
            className="placeholder:text-white-300 body-sm-medium-12"
          />
          {walletAddress && !isValidSuiAddress(walletAddress) && (
            <div className="body-xs-regular-10 text-red-600">
              Invalid wallet
            </div>
          )}
        </div>

        <AppInput
          value={walletName}
          onChange={(e: any) => setWalletName(e.target.value)}
          label="Wallet Name"
          placeholder="Enter here"
          rootClassName="!h-[34px] mb-8 !bg-transparent"
          labelClassName="text-white-700 action-sm-medium-12"
          className="placeholder:text-white-300 body-sm-medium-12"
        />
        <AppButton
          disabled={
            !walletAddress || !walletName || !isValidSuiAddress(walletAddress)
          }
          onClick={addWallets}
          variant="buy"
          size="large"
        >
          Add Wallet
        </AppButton>
      </div>
    </AppModal>
  );
};

export default ModalAddWallet;
