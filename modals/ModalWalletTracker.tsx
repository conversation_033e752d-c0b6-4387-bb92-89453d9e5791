"use client";
import React, { useC<PERSON>back, useEffect, useState } from "react";
import {
  Close,
  FlashIcon,
  SearchIcon,
  SettingsIcon,
  Setting,
} from "@/assets/icons";
import AppInput from "@/components/AppInput";
import { ModalLiveTrades } from "@/modals/ModalLiveTrades";
import { ModalQuickBuySettings } from "@/modals";
import WalletManager from "../components/WalletTracker/WalletManager";
import LiveTrades from "../components/WalletTracker/LiveTrades";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "../store/index";
import { setIsShowModalWalletTracker } from "@/store/metadata.store";
import { setWalletTracker } from "@/store/user.store";
import { AppLogoNetwork } from "@/components/AppLogoNetwork";
import { NETWORKS } from "@/utils/contants";
import { NumericFormat } from "react-number-format";
import { debounce } from "lodash";
import { isZero } from "@/utils/helper";
import { useOrder } from "@/hooks/useOrder";
import rf from "@/services/RequestFactory";
import { TGroup, TWalletTracker } from "../types/wallet-tracker";
import { Rnd } from "react-rnd";
import { useMediaQuery } from "react-responsive";
import Tooltip from "rc-tooltip";

const MENU = [
  {
    name: "Wallet Manager",
    key: "wallet-manager",
  },
  {
    name: "Live Trades",
    key: "live-trades",
  },
];

export const ModalWalletTracker = () => {
  const dispatch = useDispatch<AppDispatch>();
  const [group, setGroup] = useState<TGroup>();
  const [groups, setGroups] = useState<TGroup[]>([]);
  const [activeTab, setActiveTab] = useState<string>("wallet-manager");
  const [searchWallet, setSearchWallet] = useState<string>("");
  const [isSettingsOpen, setSettingsOpen] = useState<boolean>(false);
  const [isLiveTradesSetting, setLiveTradesSetting] = useState<boolean>(false);
  const [buyAmount, setBuyAmount] = useState<any>("");
  const [size, setSize] = useState<any>({ width: 650, height: 285 });
  const isMobile = useMediaQuery({ query: "(max-width: 768px)" });
  const accessToken = useSelector((state: RootState) => state.user.accessToken);
  const walletTracker = useSelector(
    (state: RootState) => state.user.walletTracker
  );
  const [isResizing, setIsResizing] = useState<boolean>(false);

  const { settings, updateDefaultAmountOrderBuy } = useOrder();

  const debounceUpdate = useCallback(
    debounce(
      (nextValue: string) => updateDefaultAmountOrderBuy(settings, nextValue),
      1000
    ),
    [settings]
  );

  useEffect(() => {
    if (!isZero(settings?.quickBuyAmount || 0)) {
      setBuyAmount(settings?.quickBuyAmount || 0);
    }
  }, [settings]);

  const onClose = () => {
    dispatch(setIsShowModalWalletTracker({ isShow: false }));
  };

  const getGroups = async () => {
    try {
      const res = await rf.getRequest("WalletTrackerRequest").getGroups();
      if (res.length) {
        setGroups(res);
        // Group 1st
        setGroup(res[0]);
      }
    } catch (e) {
      console.log(e);
    }
  };

  useEffect(() => {
    getGroups().then();
  }, []);

  const getWallets = async () => {
    if (!groups || !groups.length) return;
    try {
      let allWallets: any[] = [];
      for (const g of groups) {
        const res = await rf
          .getRequest("WalletTrackerRequest")
          .getWallets(g.id);
        if (Array.isArray(res)) {
          allWallets = allWallets.concat(res);
        }
      }
      dispatch(
        setWalletTracker({
          walletTracker: {
            ...walletTracker,
            wallets: allWallets,
          },
        })
      );
    } catch (e) {
      console.log(e);
    }
  };

  return (
    <>
      {/* Blocker overlay: chỉ bật khi resizing */}
      {isResizing && (
        <div
          style={{
            position: "fixed",
            top: 0,
            left: 0,
            width: "100vw",
            height: "100vh",
            zIndex: 9998,
            background: "transparent",
          }}
        />
      )}

      <Rnd
        default={{
          x: 0,
          y: 0,
          width: 650,
          height: 290,
        }}
        size={{
          width: isMobile ? "100%" : size.width,
          height: isMobile ? 325 : size.height,
        }}
        onResizeStart={() => setIsResizing(true)}
        onResizeStop={(e, dir, ref, delta, pos) => {
          setSize({ width: ref.offsetWidth, height: ref.offsetHeight });
          setIsResizing(false);
        }}
        dragHandleClassName="drag-handle"
        minHeight={290}
        enableResizing={
          isMobile
            ? false
            : { right: true, bottom: true, left: true, bottomRight: true }
        }
        disableDragging={isMobile}
        style={{
          background: `linear-gradient(0deg, #08090C, #08090C), linear-gradient(0deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.1))`,
          backgroundBlendMode: "overlay",
          boxShadow: `4px 4px 8px 0px var(--black-500)`,
        }}
        className="rounded-b-8 border-white-100 min-w-screen inset-0 z-[99] flex w-screen flex-col overflow-hidden border-b border-r bg-opacity-80 md:w-auto md:min-w-[650px]"
      >
        <div className="relative flex h-[88px] flex-col items-center justify-center p-2 md:h-[48px] md:flex-row md:justify-between">
          <div className="mb-2 flex h-[32px] w-full max-w-[359px] items-center gap-1 p-1 md:mb-0 md:w-[274px]">
            {MENU.map((item) => (
              <div
                key={item.key}
                onClick={() => setActiveTab(item.key)}
                className={`${
                  activeTab === item.key
                    ? "bg-white-100 text-white-1000"
                    : "text-white-500"
                } flex h-[24px] flex-1 cursor-pointer items-center justify-center rounded p-1 text-[12px] font-semibold leading-4`}
              >
                {item.name}
              </div>
            ))}
          </div>

          <div className="drag-handle text-white-500 hover:text-white-1000 absolute left-1/2 top-1/3 hidden -translate-x-1/2 -translate-y-1/2 rotate-90 cursor-move md:block">
            ⠿
          </div>

          <div className="border-white-100 flex w-full items-center justify-between gap-[15px] border-b pb-2 md:w-auto md:border-0">
            {activeTab === "wallet-manager" ? (
              <AppInput
                value={searchWallet}
                onChange={(e) => setSearchWallet(e.target.value)}
                icon={
                  <SearchIcon className="text-white-500 h-[16px] w-[16px]" />
                }
                placeholder="Search by name or address"
                rootClassName="bg-transparent md:w-[160px] w-[264px] h-[32px] p-2 gap-1 border-white-100 border body-sm-regular-12"
                className="placeholder:text-white-300 w-[124px] truncate placeholder:text-[12px] placeholder:font-normal placeholder:leading-[18px]"
              />
            ) : (
              <div className="flex items-center gap-2">
                <Tooltip overlay={`Live Trades Settings`} placement="bottom">
                  <div
                    className="mr-2 cursor-pointer"
                    onClick={() => setLiveTradesSetting(true)}
                  >
                    <Setting />
                  </div>
                </Tooltip>
                <div
                  className="cursor-pointer"
                  onClick={() => setSettingsOpen(true)}
                >
                  <SettingsIcon />
                </div>
                <div className="border-white-150 flex h-[32px] items-center justify-between rounded-[6px] border p-2">
                  <FlashIcon />
                  <div className="flex items-center gap-1">
                    <NumericFormat
                      disabled={!accessToken}
                      value={buyAmount}
                      onChange={(e) => {
                        setBuyAmount(e.target.value.replace(/,/g, ""));
                        if (!accessToken) return;
                        debounceUpdate(e.target.value.replace(/,/g, "") || "");
                      }}
                      thousandSeparator=","
                      valueIsNumericString
                      decimalScale={6}
                      className="action-sm-medium-12 text-white-1000 placeholder:text-white-300 w-[40px] bg-transparent text-right outline-none"
                    />
                    <AppLogoNetwork network={NETWORKS.SUI} isBase />
                  </div>
                </div>
              </div>
            )}

            <div
              onClick={onClose}
              className=" border-white-100 cursor-pointer border-l pl-6"
            >
              <Close />
            </div>
          </div>
        </div>

        {activeTab === "wallet-manager" && (
          <WalletManager
            size={size}
            searchWallet={searchWallet}
            group={group}
            fetchGroups={getGroups}
            getWallets={getWallets}
          />
        )}
        {activeTab === "live-trades" && (
          <LiveTrades size={size} groups={groups} />
        )}

        {isSettingsOpen && (
          <ModalQuickBuySettings
            isOpen={isSettingsOpen}
            onClose={() => setSettingsOpen(false)}
          />
        )}

        {isLiveTradesSetting && (
          <ModalLiveTrades
            isOpen={isLiveTradesSetting}
            onClose={() => setLiveTradesSetting(false)}
          />
        )}
      </Rnd>
    </>
  );
};
