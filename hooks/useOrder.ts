"use client";

import BigNumber from "bignumber.js";
import { useCallback, useMemo, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useUser } from "@/hooks/useUser";
import { AppBroadcast, BROADCAST_EVENTS } from "@/libs/broadcast";
import { toastError, toastInfo, toastWarning } from "@/libs/toast";
import rf from "@/services/RequestFactory";
import { AppDispatch, RootState } from "@/store";
import { getSettingsOrder } from "@/store/user.store";
import { TPair, TSettingOrder } from "@/types";
import { filterParams, isZero, minusBN, toStringBN } from "@/utils/helper";
import { isPairWithSui } from "@/utils/pair";
import {
  simulateBuyExactIn,
  simulateSellExactIn,
  TDexPool,
} from "@/utils/simulates";
import { useBalance } from "./useBalance";
import { roundNumber } from "@/utils/format";
import { SUI_TOKEN_ADDRESS_FULL } from "@/utils/contants";
import config from "@/config";

const MIN_TOKEN_BALANCE_TO_SELL = "0.00000000000001";

export const useOrder = () => {
  const settingsOrder = useSelector(
    (state: RootState) => state.user.settingsOrder
  );
  const balances = useSelector((state: RootState) => state.user.balances);
  const wallets = useSelector((state: RootState) => state.user.wallets);
  const network = useSelector((state: RootState) => state.user.network);
  const isSubmitOrder = useRef<boolean>(false);

  const { activeWalletAddresses, activeTotalSuiBalance } = useUser();
  const dispatch = useDispatch<AppDispatch>();
  const { getWalletBalanceAsync } = useBalance();

  const MIN_GAS_FEE = config.minGasFee;

  const settings = useMemo(() => {
    if (!Object.keys(settingsOrder).length) {
      return {};
    }
    return settingsOrder?.presets[settingsOrder?.defaultPreset] || {};
  }, [settingsOrder]) as TSettingOrder;

  const getMinSuiAmountToBuy = () => {
    return new BigNumber(settings?.quickBuyTipAmount || 0)
      .plus(MIN_GAS_FEE || 0.1)
      .toString(); // 0.1 to pay gas
  };

  const validateSuiBalanceForGasFee = () => {
    const minSuiAmount = getMinSuiAmountToBuy();
    if (new BigNumber(activeTotalSuiBalance).comparedTo(minSuiAmount) <= 0) {
      throw Error(
        `Insufficient sui balance to cover gas fee (min ${minSuiAmount})`
      );
    }
  };

  const getAvailableAddressesWithEnoughBalance = async (
    amount: string,
    tokenAddress: string
  ) => {
    const availableAddresses = [];
    for (const activeWallet of activeWalletAddresses || []) {
      const balance = await getWalletBalanceAsync(tokenAddress, activeWallet);
      if (new BigNumber(balance).comparedTo(amount) >= 0) {
        availableAddresses.push({
          address: activeWallet,
          balance: balance,
        });
      }
    }
    return availableAddresses;
  };

  const getRealAmountCanBuy = useCallback(
    (pair: TPair, buyAmount: string, isBuyBySuiToken = false) => {
      if (isPairWithSui(pair) || isBuyBySuiToken) {
        return BigNumber.min(
          buyAmount,
          minusBN(activeTotalSuiBalance, getMinSuiAmountToBuy())
        ).toString();
      }
      return buyAmount;
    },
    [activeTotalSuiBalance, balances, wallets]
  );

  const quickBuy = useCallback(
    async (
      autoSellSettings: any,
      pair: TPair,
      buyAmount: string,
      buyByToken?: string,
      useAggregator: boolean = false,
      onSuccess?: () => void
    ) => {
      const walletEnoughBalance = await getAvailableAddressesWithEnoughBalance(
        buyAmount,
        buyByToken || pair.tokenQuote.address
      );
      try {
        if (!buyAmount) {
          throw Error("Please input amount!");
        }
        validateSuiBalanceForGasFee();
        if (!walletEnoughBalance.length) {
          throw Error("No wallet has enough balance to buy!");
        }
        if (isSubmitOrder.current) {
          toastWarning(
            "Warning",
            "Please wait for the previous order to complete!"
          );
          return;
        }
        const maxAmountCanBuy = getRealAmountCanBuy(
          pair,
          buyAmount,
          !!buyByToken
        );
        isSubmitOrder.current = true;
        await rf.getRequest("NewOrderRequest").createOrderQuickBuy(
          pair.network,
          filterParams({
            autoSellSettings: !!autoSellSettings
              ? {
                  ...autoSellSettings,
                  triggers: autoSellSettings?.triggers?.map((trigger: any) => ({
                    ...trigger,
                    priceChangePercent: Number(trigger?.priceChangePercent),
                    sellPercent: Number(trigger?.sellPercent),
                  })),
                }
              : null,
            buyAmount: toStringBN(maxAmountCanBuy),
            orderSetting: {
              priorityFee: toStringBN(settings?.quickBuyTipAmount || 0),
              slippage: Number(settings?.quickBuySlippage || 10),
              gasPrice: +settings.quickBuyGasPrice,
            },
            pairId: pair?.pairId,
            tokenAddress: pair?.tokenBase?.address,
            wallets: walletEnoughBalance?.map((wallet) => wallet.address),
          })
        );
        toastInfo("Submitting", "Submitting Order!");
        if (typeof onSuccess === "function") {
          onSuccess();
        }
      } catch (e: any) {
        console.error("buyOrder error", e);
        toastError("Error", e.message || "Something went wrong!");
      } finally {
        isSubmitOrder.current = false;
      }
    },
    [settings, activeTotalSuiBalance, balances, activeWalletAddresses, wallets]
  );

  const buyLimit = useCallback(
    async (
      autoSellSettings: any,
      pair: TPair,
      buyAmount: string,
      targetPriceUsd: string,
      buyByToken?: string,
      useAggregator: boolean = false,
      onSuccess?: () => void
    ) => {
      try {
        if (!buyAmount) {
          throw Error("Please input amount!");
        }
        const walletEnoughBalance =
          await getAvailableAddressesWithEnoughBalance(
            buyAmount,
            buyByToken || pair.tokenQuote.address
          );
        if (!walletEnoughBalance.length) {
          throw Error("No wallet has enough balance to buy!");
        }
        await rf.getRequest("NewOrderRequest").createOrderBuyLimit(
          pair.network,
          filterParams({
            autoSellSettings,
            orderSetting: {
              priorityFee: toStringBN(settings?.limitBuyTipAmount || 0),
              slippage: Number(settings?.limitBuySlippage || 10),
              gasPrice: +settings.limitBuyGasPrice,
            },
            buyByToken,
            pairId: pair?.pairId,
            buyAmount: toStringBN(buyAmount),
            targetPriceUsd,
            wallets: walletEnoughBalance?.map((wallet) => wallet.address),
          })
        );

        toastInfo("Submitting", "Submitting Order!");
        AppBroadcast.dispatch(BROADCAST_EVENTS.FETCH_ORDERS, {});
        onSuccess?.();
      } catch (e: any) {
        console.error(e);
        toastError("Error", e.message || "Something went wrong!");
      }
    },
    [settings, activeWalletAddresses]
  );

  const buyDCA = useCallback(
    async (
      pair: TPair,
      buyAmount: string,
      interval: number | string,
      tokenPriceRange: any,
      repeat: number,
      buyByToken?: string,
      useAggregator: boolean = false,
      onSuccess?: () => void
    ) => {
      try {
        if (!buyAmount) {
          throw Error("Please input amount!");
        }

        if (!interval) {
          throw Error("Please input interval!");
        }

        if (!repeat) {
          throw Error("Please input amount order!");
        }

        if (
          tokenPriceRange &&
          !!tokenPriceRange?.min &&
          !!tokenPriceRange?.max &&
          new BigNumber(tokenPriceRange?.min).gte(tokenPriceRange?.max)
        ) {
          throw Error(
            "Minimum market cap must be less than maximum market cap!"
          );
        }

        validateSuiBalanceForGasFee();

        const walletEnoughBalance =
          await getAvailableAddressesWithEnoughBalance(
            buyAmount,
            buyByToken || pair.tokenQuote.address
          );

        if (!walletEnoughBalance.length) {
          throw Error("No wallet has enough balance to buy!");
        }

        const maxAmountCanBuy = getRealAmountCanBuy(
          pair,
          buyAmount,
          !!buyByToken
        );

        await rf.getRequest("NewOrderRequest").createOrderBuyDCA(
          pair.network,
          filterParams({
            orderSetting: {
              priorityFee: toStringBN(settings?.quickBuyTipAmount || 0),
              slippage: Number(settings?.quickBuySlippage || 10),
              gasPrice: +settings.quickBuyGasPrice,
            },
            interval,
            repeat,
            tokenPriceRange,
            buyByToken,
            pairId: pair?.pairId,
            buyAmount: toStringBN(maxAmountCanBuy),
            wallets: walletEnoughBalance?.map((wallet) => wallet.address),
          })
        );

        toastInfo("Submitting", "Submitting Order!");
        AppBroadcast.dispatch(BROADCAST_EVENTS.FETCH_ORDERS, {});
        onSuccess?.();
      } catch (e: any) {
        console.error(e);
        toastError("Error", e.message || "Something went wrong!");
      }
    },
    [settings, activeWalletAddresses]
  );

  const estimateQuickBuy = useCallback(
    async (pair: TPair, buyAmount: string, isBuyBySuiToken = false) => {
      const getTokenQuoteAddress = () => {
        if (isBuyBySuiToken && !isPairWithSui(pair)) {
          return SUI_TOKEN_ADDRESS_FULL;
        }
        return pair.tokenQuote.address;
      };

      const walletEnoughBalance = await getAvailableAddressesWithEnoughBalance(
        buyAmount,
        getTokenQuoteAddress()
      );
      try {
        if (!buyAmount) {
          return;
        }
        validateSuiBalanceForGasFee();
        if (!walletEnoughBalance.length) {
          return;
        }
        const maxAmountCanBuy = getRealAmountCanBuy(
          pair,
          buyAmount,
          isBuyBySuiToken
        );

        const pool: TDexPool = {
          dex: pair.dex.dex as any,
          objectId: pair.poolId,
        };

        const addresses = walletEnoughBalance.map((wallet) => wallet.address);
        const amountOutList = await Promise.all(
          addresses.map(async (walletAddress) => {
            try {
              return await simulateBuyExactIn(
                walletAddress,
                new BigNumber(
                  roundNumber(
                    maxAmountCanBuy,
                    BigNumber.ROUND_DOWN,
                    pair?.tokenQuote?.decimals
                  )
                ),
                pair?.tokenQuote,
                pair?.tokenBase,
                pool,
                isBuyBySuiToken,
                pair.isXQuoteToken,
                pair.feeTier
              );
            } catch (e: any) {
              console.error(e);
              return "0";
            }
          })
        );
        const totalAmountOut = amountOutList.reduce(
          (prev, amountOut) => new BigNumber(prev).plus(amountOut).toString(),
          new BigNumber(0)
        );

        return totalAmountOut.toString();
      } catch (e: any) {
        console.error(e);
        return "";
      }
    },
    [settings, activeTotalSuiBalance, balances, activeWalletAddresses, wallets]
  );

  const estimateQuickSell = useCallback(
    async (pair: TPair, sellPercent: number, walletAddresses?: string[]) => {
      if (!pair?.network || !pair?.pairId) return;
      if (isZero(sellPercent)) {
        return;
      }

      const pool: TDexPool = {
        dex: pair.dex.dex as any,
        objectId: pair.poolId,
      };

      const walletEnoughBalance = await getAvailableAddressesWithEnoughBalance(
        MIN_TOKEN_BALANCE_TO_SELL,
        pair?.tokenBase?.address
      );
      if (!walletEnoughBalance.length) {
        return;
      }

      const addresses = walletEnoughBalance.map((wallet) => wallet.address);

      const amountOutList = await Promise.all(
        addresses.map(async (walletAddress) => {
          try {
            return await simulateSellExactIn(
              walletAddress,
              sellPercent,
              pair?.tokenBase,
              pair?.tokenQuote,
              pool,
              pair?.feeTier,
              pair?.isXQuoteToken
            );
          } catch (e: any) {
            console.error(e);
            return "0";
          }
        })
      );

      const totalAmountOut = amountOutList.reduce(
        (prev, amountOut) => prev.plus(amountOut),
        new BigNumber(0)
      );

      return totalAmountOut.toString();
    },
    [settings, activeWalletAddresses, balances, wallets]
  );

  const quickSell = useCallback(
    async (
      pair: TPair,
      sellPercent: number,
      useAggregator: boolean = false,
      onSuccess?: () => void
    ) => {
      const walletEnoughBalance = await getAvailableAddressesWithEnoughBalance(
        MIN_TOKEN_BALANCE_TO_SELL,
        pair?.tokenBase?.address
      );
      if (isZero(sellPercent)) {
        return;
      }
      if (!walletEnoughBalance.length) {
        toastWarning("Warning", "No wallet has enough balance to sell");
        return;
      }
      if (isSubmitOrder.current) {
        toastWarning(
          "Warning",
          "Please wait for the previous order to complete!"
        );
        return;
      }
      isSubmitOrder.current = true;
      try {
        await rf.getRequest("NewOrderRequest").createOrderQuickSell(
          pair?.network,
          filterParams({
            orderSetting: {
              priorityFee: toStringBN(settings?.quickSellTipAmount || 0),
              slippage: Number(settings?.quickSellSlippage || 10),
              gasPrice: +settings.quickSellGasPrice,
            },
            pairId: pair?.pairId,
            sellPercent,
            tokenAddress: pair?.tokenBase?.address,
            wallets: walletEnoughBalance.map((wallet) => wallet.address),
          })
        );

        toastInfo("Submitting", "Submitting Order!");
        // dispatch(getWalletsUser({ network }));
        onSuccess?.();
      } catch (e: any) {
        console.error(e);
        toastError("Error", e.message || "Something went wrong!");
      } finally {
        isSubmitOrder.current = false;
      }
    },
    [settings, activeWalletAddresses, balances, wallets]
  );

  const sellLimit = useCallback(
    async (
      pair: TPair,
      sellPercent: number,
      targetPriceUsd: string,
      targetMcPercent: number | null,
      targetPricePercent: number | null,
      useAggregator: boolean = false,
      onSuccess?: () => void
    ) => {
      const walletEnoughBalance = await getAvailableAddressesWithEnoughBalance(
        MIN_TOKEN_BALANCE_TO_SELL,
        pair?.tokenBase?.address
      );
      if (!walletEnoughBalance.length) {
        toastWarning("Warning", "No wallet has enough balance to sell");
        return;
      }
      try {
        await rf.getRequest("NewOrderRequest").createOrderSellLimit(
          pair?.network,
          filterParams({
            orderSetting: {
              priorityFee: toStringBN(settings?.limitSellTipAmount || 0),
              slippage: Number(settings?.limitSellSlippage || 10),
              gasPrice: +settings.limitSellGasPrice,
            },
            pairId: pair?.pairId,
            sellPercent,
            targetPriceUsd,
            targetMcPercent,
            targetPricePercent,
            tokenAddress: pair?.tokenBase?.address,
            wallets: walletEnoughBalance.map((wallet) => wallet.address),
          })
        );

        toastInfo("Submitting", "Submitting Order!");
        // dispatch(getWalletsUser({ network }));
        AppBroadcast.dispatch(BROADCAST_EVENTS.FETCH_ORDERS, {});
        onSuccess?.();
      } catch (e: any) {
        console.error(e);
        toastError("Error", e.message || "Something went wrong!");
      }
    },
    [settings, activeWalletAddresses]
  );

  const sellDCA = useCallback(
    async (
      pair: TPair,
      interval: number | string,
      repeat: number,
      tokenPriceRange: any,
      sellPercent: number,
      useAggregator: boolean = false,
      onSuccess?: () => void
    ) => {
      const walletEnoughBalance = await getAvailableAddressesWithEnoughBalance(
        MIN_TOKEN_BALANCE_TO_SELL,
        pair?.tokenBase?.address
      );
      if (!walletEnoughBalance.length) {
        toastWarning("Warning", "No wallet has enough balance to sell");
        return;
      }

      if (!interval) {
        toastError("Error", "Please input interval!");
        return;
      }

      if (!repeat) {
        toastError("Error", "Please input amount order!");
        return;
      }

      if (
        tokenPriceRange &&
        tokenPriceRange?.min &&
        tokenPriceRange?.max &&
        new BigNumber(tokenPriceRange?.min).gte(tokenPriceRange?.max)
      ) {
        toastError(
          "Error",
          "Minimum market cap must be less than maximum market cap!"
        );
        return;
      }

      try {
        await rf.getRequest("NewOrderRequest").createOrderSellDCA(
          pair.network,
          filterParams({
            orderSetting: {
              priorityFee: toStringBN(settings?.quickSellTipAmount || 0),
              slippage: Number(settings?.quickSellSlippage || 10),
              gasPrice: +settings.quickSellGasPrice,
            },
            pairId: pair?.pairId,
            sellPercent,
            interval,
            repeat,
            tokenPriceRange,
            tokenAddress: pair?.tokenBase?.address,
            wallets: walletEnoughBalance.map((wallet) => wallet.address),
          })
        );

        toastInfo("Submitting", "Submitting Order!");
        // dispatch(getWalletsUser({ network }));
        AppBroadcast.dispatch(BROADCAST_EVENTS.FETCH_ORDERS, {});
        onSuccess?.();
      } catch (e: any) {
        console.error(e);
        toastError("Error", e.message || "Something went wrong!");
      }
    },
    [settings, activeWalletAddresses]
  );

  const closePosition = useCallback(
    async (
      sellPercent: number,
      tokenAddress: string,
      walletAddress: string,
      useAggregator: boolean = false,
      onSuccess?: () => void
    ) => {
      try {
        if (!sellPercent) {
          throw Error("Please input percent amount!");
        }
        if (isSubmitOrder.current) {
          toastWarning(
            "Warning",
            "Please wait for the previous order to complete!"
          );
          return;
        }
        isSubmitOrder.current = true;
        await rf.getRequest("NewOrderRequest").closePosition(
          network,
          filterParams({
            orderSetting: {
              priorityFee: settings?.quickSellTipAmount,
              slippage: settings?.quickSellSlippage,
              gasPrice: +settings.quickSellGasPrice,
            },
            sellPercent,
            tokenAddress: tokenAddress,
            walletAddress,
          })
        );

        toastInfo("Success", "Close position successfully!");
        onSuccess?.();
      } catch (e: any) {
        console.error(e);
        toastError("Error", e.message || "Something went wrong!");
      } finally {
        isSubmitOrder.current = false;
      }
    },
    [network, settings]
  );

  const updateSettings = useCallback(
    async (
      slippage: string | number,
      tipAmount: string,
      quickBuyAmount: string,
      onSuccess?: () => void
    ) => {
      try {
        await rf
          .getRequest("PresetSettingRequest")
          .updateTradeSettings(network, {
            defaultPreset: 0,
            presets: [
              {
                presetId: 0,
                quickBuyAmount,
                quickSellAmount: "0",
                slippage: +slippage,
                tipAmount: `${tipAmount}`,
              },
            ],
          });
        // toastSuccess('Success', 'Update successfully!');
        dispatch(getSettingsOrder({ network }));
        onSuccess?.();
      } catch (e: any) {
        console.error(e);
        toastError("Error", e.message || "Something went wrong!");
      }
    },
    [network]
  );

  const updateSettingsQuickOrder = useCallback(
    async (
      dataSettings: TSettingOrder,
      slippage: string | number,
      tipAmount: string,
      gasPrice: number,
      onSuccess?: () => void
    ) => {
      try {
        await rf
          .getRequest("PresetSettingRequest")
          .updateTradeSettings(network, {
            defaultPreset: 0,
            presets: [
              {
                ...dataSettings,
                presetId: 0,
                quickBuyGasPrice: +gasPrice,
                quickBuySlippage: +slippage,
                quickBuyTipAmount: `${tipAmount}`,
                quickSellGasPrice: +gasPrice,
                quickSellSlippage: +slippage,
                quickSellTipAmount: `${tipAmount}`,
              },
            ],
          });
        dispatch(getSettingsOrder({ network }));
        onSuccess?.();
      } catch (e: any) {
        console.error(e);
        toastError("Error", e.message || "Something went wrong!");
      }
    },
    [network]
  );

  const updateDefaultAmountOrderBuy = useCallback(
    async (dataSettings: TSettingOrder, quickBuyAmount: string | number) => {
      try {
        await rf
          .getRequest("PresetSettingRequest")
          .updateTradeSettings(network, {
            defaultPreset: 0,
            presets: [
              {
                ...dataSettings,
                presetId: 0,
                quickBuyAmount: `${quickBuyAmount || ""}`,
              },
            ],
          });
        dispatch(getSettingsOrder({ network }));
      } catch (e: any) {
        console.error(e);
        toastError("Error", e.message || "Something went wrong!");
      }
    },
    [network]
  );

  const updateSettingsLimitOrder = useCallback(
    async (
      dataSettings: TSettingOrder,
      slippage: string | number,
      tipAmount: string,
      gasPrice: string,
      onSuccess?: () => void
    ) => {
      try {
        await rf
          .getRequest("PresetSettingRequest")
          .updateTradeSettings(network, {
            defaultPreset: 0,
            presets: [
              {
                ...dataSettings,
                presetId: 0,
                limitBuyGasPrice: +gasPrice,
                limitBuySlippage: +slippage,
                limitBuyTipAmount: `${tipAmount}`,
                limitSellGasPrice: +gasPrice,
                limitSellSlippage: +slippage,
                limitSellTipAmount: `${tipAmount}`,
              },
            ],
          });
        dispatch(getSettingsOrder({ network }));
        onSuccess?.();
      } catch (e: any) {
        console.error(e);
        toastError("Error", e.message || "Something went wrong!");
      }
    },
    [network]
  );

  const settingsGlobal = useCallback(
    async (dataSettings: TSettingOrder, data: any, onSuccess?: () => void) => {
      try {
        await rf
          .getRequest("PresetSettingRequest")
          .updateTradeSettings(network, {
            defaultPreset: 0,
            presets: [
              {
                ...dataSettings,
                presetId: 0,
                ...data,
              },
            ],
          });
        dispatch(getSettingsOrder({ network }));
        onSuccess?.();
      } catch (e: any) {
        console.error(e);
        toastError("Error", e.message || "Something went wrong!");
      }
    },
    [network]
  );

  const updateSettingsLiveTrades = useCallback(
    async (
      dataSettings: TSettingOrder,
      slippage: string | number,
      tipAmount: string,
      gasPrice: number,
      onSuccess?: () => void
    ) => {
      try {
        await rf
          .getRequest("PresetSettingRequest")
          .updateTradeSettings(network, {
            defaultPreset: 0,
            presets: [
              {
                ...dataSettings,
                presetId: 0,
                quickBuyGasPrice: +gasPrice,
                quickBuySlippage: +slippage,
                quickBuyTipAmount: `${tipAmount}`,
                quickSellGasPrice: +gasPrice,
                quickSellSlippage: +slippage,
                quickSellTipAmount: `${tipAmount}`,
              },
            ],
          });
        dispatch(getSettingsOrder({ network }));
        onSuccess?.();
      } catch (e: any) {
        console.error(e);
        toastError("Error", e.message || "Something went wrong!");
      }
    },
    [network]
  );

  return {
    quickBuy,
    buyLimit,
    buyDCA,
    estimateQuickBuy,
    quickSell,
    estimateQuickSell,
    sellLimit,
    sellDCA,
    updateSettings,
    settings,
    closePosition,
    updateSettingsQuickOrder,
    updateSettingsLimitOrder,
    updateDefaultAmountOrderBuy,
    settingsGlobal,
    updateSettingsLiveTrades,
  };
};
