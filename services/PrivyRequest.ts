import config from "@/config";
import BaseRequest from "./BaseRequest";

export default class PrivyRequest extends BaseRequest {
  getUrlPrefix() {
    return "http://172.16.198.43:11000";
  }

  async login(idToken: string) {
    const url = `/api/v1/privy/login`;
    return this.post(url, { idToken });
  }

  async createWallet(numberWallets: number) {
    const url = `/api/v1/privy/create-wallet`;
    return this.post(url, { numberWallets });
  }
}
