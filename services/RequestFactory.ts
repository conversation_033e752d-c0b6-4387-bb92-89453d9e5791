import AlertRequest from "./AlertRequest";
import CampaignsRequest from "./CampaignsRequest";
import CandleRequest from "./CandleRequest";
import CopyTradeRequest from "./CopyTradeRequest";
import DexRequest from "./DexRequest";
import FavouriteRequest from "./FavouriteRequest";
import HolderRequest from "./HolderRequest";
import MemeRequest from "./MemeRequest";
import MyPositionRequest from "./MyPositionRequest";
import NewOrderRequest from "./NewOrderRequest";
import ExternalRequest from "./ExternalRequest";
import PairRequest from "./PairRequest";
import PresetSettingRequest from "./PresetSettingRequest";
import PriceRequest from "./PriceRequest";
import ReferralRequest from "./ReferralRequest";
import SearchRequest from "./SearchRequest";
import SnipeDexRequest from "./SnipeDexRequest";
import SnipeFunzoneRequest from "./SnipeFunzoneRequest";
import TokenRequest from "./TokenRequest";
import TradersRequest from "./TradersRequest";
import TransactionRequest from "./TransactionRequest";
import TrendingRequest from "./TrendingRequest";
import UserRequest from "./UserRequest";
import WalletRequest from "./WalletRequest";
import WithdrawRequest from "./WithdrawRequest";
import DomainRequest from "./DomainRequest";
import AuthorizeRequest from "./AuthorizeRequest";
import OauthRequest from "./OauthRequest";
import ApiKeyRequest from "./ApiKeyRequest";
import AuthRequest from "./AuthRequest";
import PrivyRequest from "./PrivyRequest";
import WebhookRequest from "./WebhookRequest";
import TokenAdvertisementRequest from "./TokenAdvertisementRequest";
import SnipeMigrationDexRequest from "./SnipeMigrationDexRequest";
import WalletTrackerRequest from "./WalletTrackerRequest";

const requestMap = {
  TransactionRequest,
  CandleRequest,
  PairRequest,
  UserRequest,
  HolderRequest,
  WalletRequest,
  TradersRequest,
  TrendingRequest,
  CampaignsRequest,
  ReferralRequest,
  FavouriteRequest,
  MyPositionRequest,
  SearchRequest,
  PriceRequest,
  DexRequest,
  WithdrawRequest,
  MemeRequest,
  NewOrderRequest,
  ExternalRequest,
  PresetSettingRequest,
  TokenRequest,
  AlertRequest,
  CopyTradeRequest,
  SnipeDexRequest,
  SnipeFunzoneRequest,
  DomainRequest,
  AuthorizeRequest,
  OauthRequest,
  ApiKeyRequest,
  AuthRequest,
  PrivyRequest,
  WebhookRequest,
  TokenAdvertisementRequest,
  SnipeMigrationDexRequest,
  WalletTrackerRequest,
};

const instances: Partial<
  Record<RequestKey, InstanceType<RequestMap[RequestKey]>>
> = {};

type RequestMap = typeof requestMap;

type RequestKey = keyof RequestMap;

export default class RequestFactory {
  static getRequest<T extends RequestKey>(
    classname: T
  ): InstanceType<RequestMap[T]> {
    const RequestClass = requestMap[classname];
    if (!RequestClass) {
      throw new Error(`Invalid request class name: ${classname}`);
    }

    let requestInstance = instances[classname];
    if (!requestInstance) {
      requestInstance = new RequestClass();
      instances[classname] = requestInstance;
    }

    return requestInstance as InstanceType<RequestMap[T]>;
  }
}
