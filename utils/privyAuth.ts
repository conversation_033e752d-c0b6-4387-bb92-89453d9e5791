export function createPrivyBasicAuth(appId: string, appSecret: string): string {
  const credentials = `${appId}:${appSecret}`;
  const encoded = btoa(credentials);
  return `Basic ${encoded}`;
}

export function createPrivyHeaders(
  appId: string,
  appSecret: string,
  authorizationSignature: any
): Record<string, string> {
  return {
    Authorization: createPrivyBasicAuth(appId, appSecret),
    "privy-app-id": appId,
    "privy-authorization-signature": authorizationSignature.signature,
    "Content-Type": "application/json",
  };
}
